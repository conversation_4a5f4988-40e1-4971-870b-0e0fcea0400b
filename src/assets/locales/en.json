{"titles": {"system": "EFAI AutoSeg", "autoSeg": "AutoSeg", "task_list": "Task List", "worklist": "Worklist", "history": "History", "settings": "Settings", "manual": "User Manual", "protocol": "Protocol", "protocols": "Protocols", "structures": "Structures", "source": "Source", "destination": "Destination", "notice": "Notice", "customized": "Customized", "source_destination": "Source & Destination", "study_info": "Study Information", "add_customized_structures": "Add Empty Structures", "add_structures": "Add Structures", "remote_server": "Remote Server", "folder": "Folder", "operation": "Operation", "resend_destination": "Resend Destination Setting", "rs": "RS", "rs_queue": "RS Queue", "rs_templates": "RS Templates", "rs_set_label": "RS Set Label", "preview": "Preview", "config_setting": "Config Setting", "patient_id": "Patient ID", "study_date": "Study Date", "study_status": "Status", "series": "Series", "series_time": "Series Time", "series_description": "Series Description", "template_description": "Template Description", "last_modified": "Last Modified", "image": "Images", "status": "Status", "name": "Name", "description": "Description", "ae_title": "AE Title", "ip": "IP", "port": "Port", "path": "Path", "dicom_tag": "DICOM Tag", "value": "Value", "structure_id": "Structure ID", "sort_number": "Sort Number", "type": "Type", "volume_type": "Volume Type", "color_code": "Color", "keyword": "Keyword", "protocol_name": "Protocol Name", "protocol_description": "Protocol Description", "study_description": "Study Description", "structure_set_label": "Structure Set Label", "use_protocol": "Use Protocol", "use_template": "Use Template", "priority": "Priority", "definition": "Definition", "all": "All", "efai_structure_name": "EFAI Structure Name", "customized_name": "Customized Name", "system_disconnected": "System Disconnected", "system_maintenance": "System Maintenance", "exceeds_file_limit": "Exceeds File Limit", "create_protocol_name": "Create a Protocol Name", "series_count": "Series Count", "format": "Format", "exact": "Exact", "regex": "RegEx", "account": "Username", "password": "Password", "transfer": "Transfer", "show_rs": "Show in RS", "process_structures_list": "Process Structures List", "operations": "Operations", "processed": "Processed", "patient_name": "Patient Name"}, "descriptions": {"process_structures_list": "After AI contouring is complete, the system will automatically generate structured contours using Boolean operations.", "process_table_empty": "Click 'Add Structure' to add and edit Process Structures.", "select_operation_structure": "Select an operation for this structure", "download": "Turn on the switches to select file types for download."}, "plain_texts": {"account": "username", "password": "password", "name": "name", "type": "type", "ae_title": "ae title", "ip": "IP", "port": "port", "path": "path", "protocol_name": "protocol name", "dicom_tag": "dicom tag", "structure_id": "structure id", "format": "format"}, "form_placeholders": {"tree_select": "Please Select", "select_volume": "Select Volume Type", "filter": "Filter", "search_name": "Search Name", "search_structure": "Search Structure...", "search_patient_id": "Search Patient Id ...", "all_study_status": "All Study Status", "start_date": "Start Date", "end_date": "End Date", "enter_variable": ["Enter", "{{variable}}"], "exact": "Exact DICOM Tag Value", "regex": "Regular Expression ", "select_structure": "Select Structure"}, "form_rules": {"dicom_tag_constraint": "Format should be like (xxxx,xxxx)", "customized_name_duplicate": "The customized names are duplicated", "min_length": ["{{variable}}", "must be at least", "{{min_length}}", "characters"], "variable_unique": ["{{variable}}", "must all be unique"], "enter_variable": ["Please enter", "{{variable}}"], "character_limitation": ["The number of characters is limited to", "{{count}}"]}, "label": {"margin_value": {"right": "X1(R)", "anterior": "Y1(A)", "inferior": "Z1(I)", "left": "X2(L)", "posterior": "Y2(P)", "superior": "Z2(S)"}, "show_records_without_rs": "Show records without RS"}, "modal_titles": {"save_settings": "Save Settings", "redraw_series": "Redraw Series", "create_new_protocol": "Create New Protocol", "new_protocol_name": "New Protocol Name", "delete_confirmation": "Confirm Deleting", "reset_confirmation": "Confirm Resetting", "change_protocol_confirmation": "Confirm Change Protocol", "leave_page_confirmation": "Leave This Page", "series_inferencing": "Series is inferencing", "remove_confirmation": "Remove Confirmation", "link_to": ["Link To", "{{value}}"]}, "modal_contents": {"save_settings": "Save the settings?", "redraw_series": "Redraw the series?", "reset_confirmation": "Reset the settings to original settings?", "change_protocol_confirmation": "Use another protocol, the current settings will not be saved.", "add_study_info_confirmation": "Add Study Information?", "add_customized_structures_confirmation": "Add empty structures?", "add_link_confirmation": "Add new link?", "leave_page_confirmation": "The changes won’t be saved if you leave this page", "wrong_with_study_info": "There is something wrong with data in \"Study Information\"", "wrong_with_customized_structures": ["There is something wrong with data in ", "{{item}}"], "delete_confirmation": ["Confirm to delete", "{{item}}?"], "item_required": ["At least 1", "{{item}}", "is required."], "multiple_items_required": ["{{item}}", "are all required."], "series_inferencing": {"first": "This Series is inferencing.", "second": "The setting won’t be saved."}, "error_no_matching": {"text": "No matching CT or RS found", "subText": "Please re-upload both the CT and RS DICOM files"}, "error_failed": {"text": "It has failed to open DICOM file", "subText": "Please re-upload the RS DICOM files."}, "remove_rs_confirmation": "Are you sure you want to remove this RS?"}, "buttons": {"": "<PERSON><PERSON>", "add": "Add", "save": "Save", "cancel": "Cancel", "close": "Close", "reset": "Reset", "ok": "OK", "next": "Next", "delete": "Delete", "remove": "Remove", "leave": "Leave", "send": "Send", "draw": "Draw", "link": "Link", "search": "Search", "clear_all": "Clear All", "yes_clear": "Yes, Clear", "no_cancel": "No, Cancel", "new_protocol": "New Protocol", "show_more": "Show More", "login": "<PERSON><PERSON>", "logout": "Logout", "all_structure": "All Structures", "selected": "Selected", "select_structures": "Select Structures", "process_structures": "Process Structures", "sort_structures": "Sort Structures", "prev": "Prev", "download": "Download", "process": "Process"}, "checkboxes": {"color_code": "Color", "remember_me": "Remember me", "not_ask_again": "Do not ask again", "select_all": "Select All"}, "radios": {"visitor": "Visitor", "registered_user": "Registered User", "exact": "Exact", "regex": "RegEx"}, "paragraphs": {"udi": "UDI", "serial_number": "Serial Number", "connection_identity": "Connection Identity", "config_changed": "The Structure has changed. Setting won’t be stored in the protocol, only used this time.", "choose_destination": ["<PERSON><PERSON>", "{{chooseCount}}", "destination"], "create_new_protocol": {"first": "Create by a blank protocol", "second": "OR", "third": "By a existing protocol to edit"}}, "switch_options": {"active": "Active", "inactive": "Inactive", "pending": "Pending", "cancelled": "Cancelled", "send_to_destination": "Send to Destination"}, "tooltips": {"worklist_remove": "Remove the study out of the AI inference list.", "worklist_suspend": "Suspend the study in AI inference list.", "worklist_start": "Start and add the Study to AI inference list.", "worklist_prioritize": "Prioritize the study to the AI inference list.", "worklist_redraw": "the study out of the AI inference list.", "worklist_resend": "the corresponding files.", "worklist_download": " the corresponding files.", "process_structures": "based on the RS Operation list.", "worklist_status_switch": "Switch to remove/start the Series out of the AI inference list.", "worklist_operation": "Modify the study setting.", "worklist_preview": "the image with generated results if available.", "structure_set_label": "Enter plain text or a regular expression (e.g., @{(xxxx,xxxx)r{}r}@), but TPS will only display the first 16 characters.", "operations": {"empty_structure": "Empty Structure", "combine": "Combine", "crop": "Crop", "overlap": "Overlap", "margin": "<PERSON><PERSON>", "outer": "<PERSON><PERSON>", "inner": "<PERSON><PERSON>"}}, "error_titles": {"": "Error", "server_error": "Server Error", "save": "Save Error", "draw": "Draw Error", "resend": "<PERSON><PERSON><PERSON>", "download": "Download Error", "link": "<PERSON>"}, "error_contents": {"server_error": "Oops! There's something wrong with server.", "choose_protocol": ["Choose a", "{{item}}", "is required before go to next step."], "link": "Please check your link information.", "no_data": "System error. Unable to display the image.", "destination_switch_error": "Turn on at least one switch, or no files will be transferred."}, "directions": {"send_to_destination_open": {"1": "You can now configure one or more Destinations and set up file transfer settings.", "2": "If you don’t want files to be transferred after AI inference is complete, turn the switch OFF."}, "send_to_destination_close": {"1": "Files will not be transferred when AI inference is complete. All structures will remain in the system.", "2": "If you want files to be transferred, turn the switch ON."}, "confirm_clear_all": "Are you sure you want to clear all content on this page?"}, "FAQ": {"detail": ["本頁面提供了有關 EFAI Auto Seg 的常見疑問與對應解法", "如果您對特定功能的操作有疑問，請使用左側的索引來快速找到您需要的資訊"]}}