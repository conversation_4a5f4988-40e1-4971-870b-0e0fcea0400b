import { useState } from 'react'

import { Button } from 'antd'
import type { TabsProps, ButtonProps } from 'antd'

import Loading from 'src/components/Loading'
import CreateNewFinalModal from 'src/components/Modal/CreateNewFinalModal'
import { StudyInfoTable } from 'src/components/Table'
import ConfigTabItemLabel from 'src/components/Tabs/ConfigTabItemLabel'
import type { ConfigDescriptionItem } from 'src/context/moduleList'
import { useAntModal } from 'src/hooks/useAntModal'
import useInvalidField from 'src/hooks/useInvalidField'
import i18n from 'src/i18n'
import { RemoteTransferTabs } from 'src/layouts'
import { BasicLayout, StructuresTabs } from 'src/layouts/NewConfigLayout'
import { useAppSelector } from 'src/store/hook'
import { verifyDetailState } from 'src/utils/verify'

import useProtocolDetailState from './hooks/useProtocolDetailState'

function CreateProtocolPage() {
  // redux
  const { configRequired } = useAppSelector((state) => state.configReducer)
  // state
  const [tabKey, setTabKey] = useState<string>('1')
  // hook
  const {
    detailState, loading, studyInfoHandlers, leaveBlocker,
    handleChangeInput, handleUpdateRemote, setRouterBlocker, protocolSaved,
  } = useProtocolDetailState('create')
  const { verifyDetail } = useInvalidField(detailState)

  // click button
  const handleNextTab = () => setTabKey((Number(tabKey) + 1).toString())

  const handlePrevTab = () => {
    setTabKey((Number(tabKey) - 1).toString())
  }

  const corfirmSaveModal = useAntModal({
    triggerProps: {
      onClick: verifyDetail,
    },
    modalProps: {
      async onOk() {
        try {
          await verifyDetail()
          await protocolSaved()
        } catch (error) {
          // log
        }
      },
    },
  })

  // disabled
  const isSourceDisabled: boolean = verifyDetailState.remote(detailState.source)
  const isStructuresDisabled: boolean = !detailState.structures.length
  const isStudyInfoDisabled: boolean = verifyDetailState.studyInfo(detailState.study_info)

  const nextButton: Record<string, ButtonProps> = {
    1: {
      children: i18n.t('buttons.next'),
      disabled: isSourceDisabled,
      onClick: handleNextTab,
    },
    2: {
      children: i18n.t('buttons.next'),
      disabled: isStudyInfoDisabled,
      onClick: handleNextTab,
    },
    3: {
      children: i18n.t('buttons.save'),
      disabled: isStructuresDisabled,
      ...corfirmSaveModal.triggerProps,
    },
  }

  // data
  const configHeader: ConfigDescriptionItem[] = [
    {
      label: 'protocol_name',
      required: true,
      type: 'input',
      props: {
        value: detailState.protocol_name,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('protocol_name', e.target.value)
        },
        status: configRequired.protocol_name && 'error',
      },
    },
    {
      label: 'description',
      type: 'input',
      props: {
        value: detailState.description,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('description', e.target.value)
        },
      },
    },
    {
      label: 'structure_set_label',
      type: 'input',
      required: true,
      tooltip: i18n.t('tooltips.structure_set_label'),
      props: {
        value: detailState.structure_set_label,
        onChange(e: React.ChangeEvent<HTMLInputElement>) {
          handleChangeInput('structure_set_label', e.target.value)
        },
        status: configRequired.structure_set_label && 'error',
      },
    },
    {
      label: 'status',
      type: 'switch',
      value: detailState.status,
      props: {
        checked: detailState.status === 'ACTIVE',
        onChange(value: boolean) {
          handleChangeInput('status', value ? 'ACTIVE' : 'INACTIVE')
        },
      },
    },
  ]

  const tabItems: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <ConfigTabItemLabel warn={configRequired.source || configRequired.destination}>
          {i18n.t('titles.source_destination')}
        </ConfigTabItemLabel>
      ),
      children: <RemoteTransferTabs detail={detailState} onUpdateRemote={handleUpdateRemote} />,
    },
    {
      key: '2',
      label: (
        <ConfigTabItemLabel warn={(configRequired.study_info)}>
          {i18n.t('titles.study_info')}
        </ConfigTabItemLabel>
      ),
      children: (<StudyInfoTable {...studyInfoHandlers} />),
      disabled: isSourceDisabled,
    },
    {
      key: '3',
      label: (
        <ConfigTabItemLabel warn={(configRequired.structures || !!configRequired.customized_structures.length)}>
          {i18n.t('titles.structures')}
        </ConfigTabItemLabel>
      ),
      children: (<StructuresTabs />),
      disabled: isSourceDisabled || isStudyInfoDisabled,
    },
  ]

  return (
    <>
      <BasicLayout
        header={{
          navigatePage: '/protocols',
          children: i18n.t('modal_titles.new_protocol_name'),
        }}
        descriptionData={configHeader}
        descriptionsProps={{
          column: {
            md: 2, lg: 2, xl: 2, xxl: 2,
          },
        }}
        loading={loading}
        tabs={tabItems}
        tabsProps={{
          activeKey: tabKey,
          onChange: setTabKey,
        }}
        footer={(
          <>
            {Number(tabKey) > 1 && (
              <Button
                htmlType="button"
                variant="outlined"
                color="primary"
                style={{ width: 100 }}
                onClick={handlePrevTab}
              >{i18n.t('buttons.prev')}
              </Button>
            )}
            <Button
              variant="outlined"
              color="primary"
              style={{ width: 100 }}
              {...nextButton[tabKey]}
            />
          </>
        )}
        leaveBlocker={leaveBlocker}
      />
      <Loading open={loading} />
      <CreateNewFinalModal
        detail={detailState}
        descriptionData={configHeader}
        setRouterBlocker={setRouterBlocker}
        {...corfirmSaveModal.modalProps}
      />
    </>
  )
}

export default CreateProtocolPage
