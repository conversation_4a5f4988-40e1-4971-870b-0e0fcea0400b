import { useRef } from 'react'

import { Form, Flex } from 'antd'

import { requiredRules } from 'src/utils/verify'

import { SecondStepConfig } from './types'

interface SecondStepProps {
  config: SecondStepConfig
  selectedId: string | number
  onRadioChange: (value: string | number) => void
}

function SecondStep({ config, selectedId, onRadioChange }: SecondStepProps) {
  const radioRefs: { current: { [key: string]: HTMLInputElement | null } } = useRef({})

  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onRadioChange(e.target.id)
  }

  return (
    <Form.Item
      name="radio-group"
      className="radio-group"
      style={{ marginBottom: 0, width: '100%' }}
      rules={[requiredRules()]}
    >
      <main id="radio-group" style={{ height: '100%' }}>
        <Flex vertical justify="center" align="center">
          <div>
            <label htmlFor="create-option" className="radio-button">
              <span>
                <input
                  id="create-option"
                  type="radio"
                  name="radio-group"
                  checked={selectedId === 'create-option'}
                  onChange={handleRadioChange}
                  ref={(input) => { radioRefs.current['create-option'] = input }}
                />
                <span />
              </span>
              <span className="radio-button-text">
                {config.createNewOptionText}
              </span>
            </label>
          </div>
          <p>{config.subtitle}</p>
          <h3>{config.description}</h3>
        </Flex>
        {config.tableComponent}
      </main>
    </Form.Item>
  )
}

export default SecondStep
