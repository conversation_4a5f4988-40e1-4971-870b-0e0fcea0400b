import { Form, Input } from 'antd'

import { requiredRules } from 'src/utils/verify'

import { FirstStepConfig } from './types'

interface FirstStepProps {
  config: FirstStepConfig
  isVerify: { check: boolean; text: string }
}

function FirstStep({ config, isVerify }: FirstStepProps) {
  return (
    <Form.Item
      label={<span>{config.label}</span>}
      name="create-name"
      htmlFor="create-name"
      layout="vertical"
      validateStatus={isVerify.check ? 'error' : undefined}
      help={isVerify.text}
      rules={config.validationRules || [requiredRules()]}
      style={{ width: '35%', minWidth: 300, textAlign: 'center' }}
    >
      <Input
        id="create-name"
        placeholder={config.placeholder}
        style={{ textAlign: 'center' }}
        maxLength={config.maxLength || 16}
      />
    </Form.Item>
  )
}

export default FirstStep
