# Generic Two-Step Modal 重構完成

## 📁 文件結構

```
src/layouts/
├── generic-two-step/
│   ├── index.tsx          # 主要 Modal 組件 (使用 Form.Provider)
│   ├── FirstStep.tsx      # 第一步驟獨立組件
│   ├── SecondStep.tsx     # 第二步驟獨立組件
│   ├── types.ts           # TypeScript 類型定義
│   └── README.md          # 本文檔
├── GenericTwoStepModal.tsx        # 重新導出文件
├── GenericTwoStepModal.example.tsx # 使用範例
└── GenericTwoStepModal.md         # 詳細文檔
```

## 🔧 重構內容

### 1. 模組化拆分
- **FirstStep.tsx**: 獨立的第一步驟組件，處理輸入表單
- **SecondStep.tsx**: 獨立的第二步驟組件，處理選擇邏輯
- **index.tsx**: 主要 Modal 組件，整合兩個步驟

### 2. 表單管理改進
- 使用 `Form.Provider` 進行表單連動
- 每個步驟都有獨立的 `Form` 實例：
  - `first-step-form`: 處理第一步驟的輸入驗證
  - `second-step-form`: 處理第二步驟的選擇驗證
- 支援跨表單的數據傳遞和驗證

### 3. 類型安全改進
- 統一的 TypeScript 類型定義在 `types.ts`
- 錯誤處理使用 `unknown` 類型而非 `any`
- 完整的介面定義，繼承自 `ModalHooksReturn`

## 🚀 使用方式

### 基本導入
```tsx
import { GenericTwoStepModal } from 'src/layouts'
// 或者
import GenericTwoStepModal from 'src/layouts/generic-two-step'
```

### 獨立組件使用
```tsx
import { FirstStep, SecondStep } from 'src/layouts/generic-two-step'
```

### 完整範例
參考 `GenericTwoStepModal.example.tsx` 中的實現範例。

## 🔄 表單連動機制

### Form.Provider 配置
```tsx
<Form.Provider
  onFormFinish={(name, { values, forms }) => {
    if (name === 'first-step-form') {
      handleFirstPage()
    } else if (name === 'second-step-form') {
      handleSecondPage()
    }
  }}
>
  {/* 步驟組件 */}
</Form.Provider>
```

### 獨立表單實例
- `firstStepForm`: 管理第一步驟的表單狀態
- `secondStepForm`: 管理第二步驟的表單狀態
- 支援獨立的驗證和重置操作

## 🎯 優勢

1. **模組化**: 每個步驟都是獨立組件，便於維護和測試
2. **可重用性**: 可以單獨使用步驟組件或整個 Modal
3. **類型安全**: 完整的 TypeScript 支援
4. **表單管理**: 使用 Ant Design 的最佳實踐進行表單連動
5. **向後相容**: 保持原有的 API 介面不變

## 🔧 技術細節

### 表單狀態管理
- 使用 `useState` 管理步驟間的共享狀態
- 表單重置時會清理所有相關狀態
- 支援錯誤狀態的獨立管理

### 組件通信
- 父組件通過 props 傳遞配置和回調函數
- 子組件通過回調函數向父組件報告狀態變化
- 使用 Form.Provider 實現表單間的數據共享

### 錯誤處理
- 統一的錯誤處理機制
- 支援自定義錯誤訊息
- 類型安全的錯誤處理函數

## 📝 遷移指南

如果您之前使用舊版本的 `GenericTwoStepModal`，新版本完全向後相容，無需修改現有代碼。

如果您想使用新的模組化功能：
1. 導入獨立的步驟組件
2. 使用 `Form.Provider` 包裝您的自定義實現
3. 參考範例文件進行配置
