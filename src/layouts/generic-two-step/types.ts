import { ReactNode } from 'react'

import { ModalHooksReturn } from 'src/hooks/useAntModal'

export interface FirstStepConfig {
  label: string
  placeholder: string
  maxLength?: number
  validationRules?: any[]
}

export interface SecondStepConfig {
  title: string
  subtitle: string
  description: string
  createNewOptionText: string
  tableComponent: ReactNode
}

export interface StepFormData {
  [key: string]: any
}

export interface GenericTwoStepModalProps extends Omit<ModalHooksReturn<unknown>, 'triggerProps'> {
  // Configuration for first step
  firstStepConfig: FirstStepConfig

  // Configuration for second step
  secondStepConfig: SecondStepConfig

  // API and business logic functions
  onFirstStepSubmit: (value: string) => Promise<void>
  onSecondStepSubmit: (selectedId: string | number, inputValue: string) => Promise<void>

  // Modal titles and texts
  errorModalTitle?: string

  // Error handling
  onError?: (error: unknown) => string
  onSelectionError?: () => string
}
