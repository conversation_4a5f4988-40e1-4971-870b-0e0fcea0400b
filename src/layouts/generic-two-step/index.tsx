import { useState, useEffect } from 'react'

import {
  <PERSON><PERSON>, ConfigProvider, Form, Modal,
} from 'antd'

import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

import FirstStep from './FirstStep'
import SecondStep from './SecondStep'
import { GenericTwoStepModalProps } from './types'

import 'src/styles/components/modal.css'

function GenericTwoStepModal({
  firstStepConfig,
  secondStepConfig,
  onFirstStepSubmit,
  onSecondStepSubmit,
  onError,
  onSelectionError,
  ...props
}: GenericTwoStepModalProps) {
  // forms
  const [firstStepForm] = Form.useForm()
  const [secondStepForm] = Form.useForm()

  // state
  const [selectedId, setSelectedId] = useState<number | string>('')
  const [inputValue, setInputValue] = useState<string>()
  const [currentPage, setCurrentPage] = useState<1 | 2>(1)
  const [errorText, setErrorText] = useState<string>('')
  const [isVerify, setIsVerify] = useState({ check: false, text: '' })

  // modal
  const leaveModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.leave_page_confirmation'),
      okText: i18n.t('buttons.leave'),
      onOk: async () => {
        props.dismiss()
        setCurrentPage(1)
        firstStepForm.resetFields()
        secondStepForm.resetFields()
      },
      className: 'confirm-modal',
      centered: true,
    },
  })

  const requiredModal = useAntModal({
    modalProps: {
      centered: true,
      className: 'confirm-modal',
      okText: i18n.t('buttons.ok'),
      footer: null,
      styles: {
        body: { padding: '7.5rem 2.5rem' },
      },
    },
  })

  const handleRadioChange = (value: number | string) => {
    setSelectedId(value)
    // Update second step form value
    secondStepForm.setFieldsValue({ 'radio-group': value })
  }

  const handleCancel = () => {
    if (currentPage === 1) {
      props.dismiss()
      setCurrentPage(1)
    } else {
      leaveModal.trigger()
      setSelectedId('')
    }
    setIsVerify({ check: false, text: '' })
    firstStepForm.resetFields()
    secondStepForm.resetFields()
  }

  const handleSecondPage = async () => {
    await onSecondStepSubmit(selectedId, inputValue!)
    setCurrentPage(1)
    firstStepForm.resetFields()
    secondStepForm.resetFields()
  }

  const handleFirstPageError = (e: unknown) => {
    if (typeof e === 'object' && e !== null) {
      const text: string = onError ? onError(e) : 'An error occurred'
      setIsVerify({ check: true, text })
    }
  }

  const handleFirstPage = async () => {
    try {
      const formValue = firstStepForm.getFieldsValue()[firstStepConfig.formItemName]
      await onFirstStepSubmit(formValue)
      setInputValue(formValue)
      setCurrentPage(2)
      setIsVerify({ check: false, text: '' })
    } catch (e) {
      handleFirstPageError(e)
    }
  }

  const handleError = () => {
    const errorMessage = onSelectionError ? onSelectionError() : 'Please make a selection'
    setErrorText(errorMessage)
    requiredModal.trigger()
  }

  const handleFinish = async () => {
    try {
      if (currentPage === 1) {
        await firstStepForm.validateFields()
        await handleFirstPage()
      } else {
        await secondStepForm.validateFields()
        await handleSecondPage()
      }
    } catch (e) {
      if (currentPage === 2) {
        handleError()
      }
    }
  }

  useEffect(() => {
    if (!props.modalProps.open) {
      return () => {
        firstStepForm.resetFields()
        secondStepForm.resetFields()
        setCurrentPage(1)
        setSelectedId('')
        setInputValue(undefined)
        setIsVerify({ check: false, text: '' })
      }
    }
    return () => { }
  }, [props.modalProps.open])

  return (
    <>
      <ConfigProvider theme={{
        components: {
          Table: {
            borderColor: 'rgba(192, 192, 192, 0.30)',
            headerBg: color.gray[3],
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[1].default,
            rowSelectedHoverBg: color.gray[1].default,
          },
        },
      }}
      >
        <Modal
          {...props.modalProps}
          onCancel={handleCancel}
          footer={[
            <Button
              htmlType="button"
              variant="outlined"
              color="primary"
              onClick={handleCancel}
              key="cancel"
            >
              {i18n.t('buttons.cancel')}
            </Button>,
            <Button
              htmlType="submit"
              variant="outlined"
              color="primary"
              onClick={handleFinish}
              key="next"
            >
              {i18n.t('buttons.next')}
            </Button>,
          ]}
        >
          <Form.Provider
            onFormFinish={(name, { values, forms }) => {
              if (name === 'first-step-form') {
                handleFirstPage()
              } else if (name === 'second-step-form') {
                handleSecondPage()
              }
            }}
          >
            {currentPage === 1 && (
              <Form
                form={firstStepForm}
                name="first-step-form"
                colon={false}
                layout="vertical"
                requiredMark={false}
                onFinish={handleFirstPage}
              >
                <FirstStep
                  config={firstStepConfig}
                  isVerify={isVerify}
                />
              </Form>
            )}

            {currentPage === 2 && (
              <Form
                form={secondStepForm}
                name="second-step-form"
                colon={false}
                layout="vertical"
                requiredMark={false}
                onFinish={handleSecondPage}
              >
                <SecondStep
                  config={secondStepConfig}
                  selectedId={selectedId}
                  onRadioChange={handleRadioChange}
                />
              </Form>
            )}
          </Form.Provider>
        </Modal>
      </ConfigProvider>

      {/* leave modal */}
      <Modal {...leaveModal.modalProps}>
        {i18n.t('modal_contents.leave_page_confirmation')}
      </Modal>

      {/* required modal */}
      <Modal {...requiredModal.modalProps}>
        {errorText}
      </Modal>
    </>
  )
}

export default GenericTwoStepModal
export { FirstStep, SecondStep }
export type { GenericTwoStepModalProps, FirstStepConfig, SecondStepConfig } from './types'
