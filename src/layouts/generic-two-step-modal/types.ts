import { TableProps } from 'antd'
import { Rule } from 'antd/es/form'

import { ModalHooksReturn } from 'src/hooks/useAntModal'

export interface FirstStepConfig {
  label: string
  placeholder: string
  maxLength?: number
  validationRules?: Rule[]
}

// Base interface for table data that must have an id field
export interface TableDataWithId {
  id: string | number
  [key: string]: unknown
}

export interface SecondStepConfig {
  title: string
  description: string
  createNewOptionText: string
  tableProps: TableProps<TableDataWithId>
}

export interface StepFormData {
  [key: string]: unknown
}

export interface GenericTwoStepModalProps extends Omit<ModalHooksReturn<unknown>, 'triggerProps'> {
  // Configuration
  firstStepConfig: FirstStepConfig
  secondStepConfig: SecondStepConfig

  // API and business logic functions
  onFirstStepSubmit: (value: string) => Promise<void>
  onSecondStepSubmit: (selectedId: string | number, inputValue: string) => Promise<void>

  // Modal titles and texts
  errorModalTitle?: string

  // Error handling
  onError?: (error: unknown) => string
  onSelectionError?: () => string
}
