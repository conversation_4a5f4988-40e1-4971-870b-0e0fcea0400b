import { TableProps } from 'antd'
import { Rule } from 'antd/es/form'

import { ModalHooksReturn } from 'src/hooks/useAntModal'

export interface FirstStepConfig {
  label: string
  placeholder: string
  maxLength?: number
  validationRules?: Rule[]
}

// 約束類型必須有 id 字段
export type WithId<T = Record<string, unknown>> = T & { id: string | number }

export interface SecondStepConfig<T extends WithId = WithId> {
  title: string
  description: string
  createNewOptionText: string
  errorMessage?: string
  tableProps: TableProps<T>
}

export interface GenericTwoStepModalProps<T extends WithId = WithId>
  extends Omit<ModalHooksReturn<unknown>, 'triggerProps'> {
  // Configuration
  firstStepConfig: FirstStepConfig
  secondStepConfig: SecondStepConfig<T>

  // API and business logic functions
  onFirstStepSubmit: (value: string) => Promise<void>
  onSecondStepSubmit: (selectedId: string | number, inputValue: string) => Promise<void>

  // Error handling
  onError?: (error: unknown) => string
}
