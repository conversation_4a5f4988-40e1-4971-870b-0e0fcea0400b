import {
  createContext, useContext, useReducer, ReactNode, useMemo,
} from 'react'

import { FirstStepConfig, SecondStepConfig } from './types'

// State interface
interface TwoStepModalState {
  currentStep: 1 | 2
  selectedId: string | number
  inputValue: string
  isVerify: { check: boolean; text: string }
  errorText: string
}

// Action types
type TwoStepModalAction =
  | { type: 'SET_CURRENT_STEP'; payload: 1 | 2 }
  | { type: 'SET_SELECTED_ID'; payload: string | number }
  | { type: 'SET_INPUT_VALUE'; payload: string }
  | { type: 'SET_VERIFY'; payload: { check: boolean; text: string } }
  | { type: 'SET_ERROR_TEXT'; payload: string }
  | { type: 'RESET_STATE' }

// Initial state
const initialState: TwoStepModalState = {
  currentStep: 1,
  selectedId: '',
  inputValue: '',
  isVerify: { check: false, text: '' },
  errorText: '',
}

// Reducer
function twoStepModalReducer(
  state: TwoStepModalState,
  action: TwoStepModalAction,
): TwoStepModalState {
  switch (action.type) {
    case 'SET_CURRENT_STEP':
      return { ...state, currentStep: action.payload }
    case 'SET_SELECTED_ID':
      return { ...state, selectedId: action.payload }
    case 'SET_INPUT_VALUE':
      return { ...state, inputValue: action.payload }
    case 'SET_VERIFY':
      return { ...state, isVerify: action.payload }
    case 'SET_ERROR_TEXT':
      return { ...state, errorText: action.payload }
    case 'RESET_STATE':
      return initialState
    default:
      return state
  }
}

// Context interface
interface TwoStepModalContextType {
  state: TwoStepModalState
  dispatch: React.Dispatch<TwoStepModalAction>
  config: {
    firstStep: FirstStepConfig
    secondStep: SecondStepConfig
  }
  handlers: {
    onFirstStepSubmit: (value: string) => Promise<void>
    onSecondStepSubmit: (selectedId: string | number, inputValue: string) => Promise<void>
    onError?: (error: unknown) => string
    onSelectionError?: () => string
  }
}

// Create context
const TwoStepModalContext = createContext<TwoStepModalContextType | null>(null)

// Provider props
interface TwoStepModalProviderProps {
  children: ReactNode
  firstStepConfig: FirstStepConfig
  secondStepConfig: SecondStepConfig
  onFirstStepSubmit: (value: string) => Promise<void>
  onSecondStepSubmit: (selectedId: string | number, inputValue: string) => Promise<void>
  onError?: (error: unknown) => string
  onSelectionError?: () => string
}

// Provider component
export function TwoStepModalProvider({
  children,
  firstStepConfig,
  secondStepConfig,
  onFirstStepSubmit,
  onSecondStepSubmit,
  onError,
  onSelectionError,
}: TwoStepModalProviderProps) {
  const [state, dispatch] = useReducer(twoStepModalReducer, initialState)

  const contextValue: TwoStepModalContextType = useMemo(() => ({
    state,
    dispatch,
    config: {
      firstStep: firstStepConfig,
      secondStep: secondStepConfig,
    },
    handlers: {
      onFirstStepSubmit,
      onSecondStepSubmit,
      onError,
      onSelectionError,
    },
  }), [
    state,
    dispatch,
    firstStepConfig,
    secondStepConfig,
    onFirstStepSubmit,
    onSecondStepSubmit,
    onError,
    onSelectionError,
  ])

  return (
    <TwoStepModalContext.Provider value={contextValue}>
      {children}
    </TwoStepModalContext.Provider>
  )
}

// Custom hook to use context
export function useTwoStepModal() {
  const context = useContext(TwoStepModalContext)
  if (!context) {
    throw new Error('useTwoStepModal must be used within a TwoStepModalProvider')
  }
  return context
}

// Action creators for convenience
export const twoStepModalActions = {
  setCurrentStep: (step: 1 | 2): TwoStepModalAction => ({
    type: 'SET_CURRENT_STEP',
    payload: step,
  }),
  setSelectedId: (id: string | number): TwoStepModalAction => ({
    type: 'SET_SELECTED_ID',
    payload: id,
  }),
  setInputValue: (value: string): TwoStepModalAction => ({
    type: 'SET_INPUT_VALUE',
    payload: value,
  }),
  setVerify: (verify: { check: boolean; text: string }): TwoStepModalAction => ({
    type: 'SET_VERIFY',
    payload: verify,
  }),
  setErrorText: (text: string): TwoStepModalAction => ({
    type: 'SET_ERROR_TEXT',
    payload: text,
  }),
  resetState: (): TwoStepModalAction => ({
    type: 'RESET_STATE',
  }),
}
