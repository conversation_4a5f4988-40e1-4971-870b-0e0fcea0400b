import { useEffect } from 'react'

import {
  <PERSON><PERSON>, ConfigProvider, Form, Modal,
} from 'antd'

import { useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'

import { TwoStepModalProvider, useTwoStepModal, twoStepModalActions } from './context'
import FirstStep from './FirstStep'
import SecondStep from './SecondStep'
import { GenericTwoStepModalProps } from './types'

import 'src/styles/components/modal.css'

type OmitKeys = 'firstStepConfig' | 'secondStepConfig' | 'onFirstStepSubmit' | 'onSecondStepSubmit'
  | 'onError' | 'onSelectionError'

type OmitGenericTwoStepModalProps = Omit<GenericTwoStepModalProps, OmitKeys>

interface TwoStepModalContentProps extends OmitGenericTwoStepModalProps { }

// Internal component that uses the context
function TwoStepModalContent({ ...props }: TwoStepModalContentProps) {
  const { state, dispatch, handlers } = useTwoStepModal()

  // forms
  const [firstStepForm] = Form.useForm()
  const [secondStepForm] = Form.useForm()

  // modal
  const leaveModal = useAntModal({
    modalProps: {
      title: i18n.t('modal_titles.leave_page_confirmation'),
      okText: i18n.t('buttons.leave'),
      onOk: async () => {
        props.dismiss()
        dispatch(twoStepModalActions.resetState())
        firstStepForm.resetFields()
        secondStepForm.resetFields()
      },
      className: 'confirm-modal',
      centered: true,
    },
  })

  const requiredModal = useAntModal({
    modalProps: {
      centered: true,
      className: 'confirm-modal',
      okText: i18n.t('buttons.ok'),
      footer: null,
      styles: {
        body: { padding: '7.5rem 2.5rem' },
      },
    },
  })

  const handleCancel = () => {
    if (state.currentStep === 1) {
      props.dismiss()
      dispatch(twoStepModalActions.setCurrentStep(1))
    } else {
      leaveModal.trigger()
      dispatch(twoStepModalActions.setSelectedId(''))
    }
    dispatch(twoStepModalActions.setVerify({ check: false, text: '' }))
    firstStepForm.resetFields()
    secondStepForm.resetFields()
  }

  const handleSecondPage = async () => {
    await handlers.onSecondStepSubmit(state.selectedId, state.inputValue!)
    dispatch(twoStepModalActions.setCurrentStep(1))
    firstStepForm.resetFields()
    secondStepForm.resetFields()
  }

  const handleFirstPageError = (e: unknown) => {
    if (typeof e === 'object' && e !== null) {
      const text: string = handlers.onError ? handlers.onError(e) : 'An error occurred'
      dispatch(twoStepModalActions.setVerify({ check: true, text }))
    }
  }

  const handleFirstPage = async () => {
    try {
      const formValue = firstStepForm.getFieldsValue()['create-name']
      await handlers.onFirstStepSubmit(formValue)
      dispatch(twoStepModalActions.setInputValue(formValue))
      dispatch(twoStepModalActions.setCurrentStep(2))
      dispatch(twoStepModalActions.setVerify({ check: false, text: '' }))
    } catch (e) {
      handleFirstPageError(e)
    }
  }

  const handleError = () => {
    const errorMessage = handlers.onSelectionError ? handlers.onSelectionError() : 'Please make a selection'
    dispatch(twoStepModalActions.setErrorText(errorMessage))
    requiredModal.trigger()
  }

  const handleFinish = async () => {
    try {
      if (state.currentStep === 1) {
        await firstStepForm.validateFields()
        await handleFirstPage()
      } else {
        await secondStepForm.validateFields()
        await handleSecondPage()
      }
    } catch (e) {
      if (state.currentStep === 2) {
        handleError()
      }
    }
  }

  useEffect(() => {
    if (!props.modalProps.open) {
      return () => {
        firstStepForm.resetFields()
        secondStepForm.resetFields()
        dispatch(twoStepModalActions.resetState())
      }
    }
    return () => { }
  }, [props.modalProps.open])

  return (
    <>
      <ConfigProvider theme={{
        components: {
          Table: {
            borderColor: 'rgba(192, 192, 192, 0.30)',
            headerBg: color.gray[3],
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[1].default,
            rowSelectedHoverBg: color.gray[1].default,
          },
        },
      }}
      >
        <Modal
          {...props.modalProps}
          onCancel={handleCancel}
          footer={[
            <Button
              htmlType="button"
              variant="outlined"
              color="primary"
              onClick={handleCancel}
              key="cancel"
            >
              {i18n.t('buttons.cancel')}
            </Button>,
            <Button
              htmlType="submit"
              variant="outlined"
              color="primary"
              onClick={handleFinish}
              key="next"
            >
              {i18n.t('buttons.next')}
            </Button>,
          ]}
        >
          <Form.Provider
            onFormFinish={(name) => {
              if (name === 'first-step-form') {
                handleFirstPage()
              } else if (name === 'second-step-form') {
                handleSecondPage()
              }
            }}
          >
            {state.currentStep === 1 && (
              <Form
                form={firstStepForm}
                name="first-step-form"
                colon={false}
                layout="vertical"
                requiredMark={false}
                onFinish={handleFirstPage}
              >
                <FirstStep />
              </Form>
            )}

            {state.currentStep === 2 && (
              <Form
                form={secondStepForm}
                name="second-step-form"
                colon={false}
                layout="vertical"
                requiredMark={false}
                onFinish={handleSecondPage}
              >
                <SecondStep />
              </Form>
            )}
          </Form.Provider>
        </Modal>
      </ConfigProvider>

      {/* leave modal */}
      <Modal {...leaveModal.modalProps}>
        {i18n.t('modal_contents.leave_page_confirmation')}
      </Modal>

      {/* required modal */}
      <Modal {...requiredModal.modalProps}>
        {state.errorText}
      </Modal>
    </>
  )
}

// Main component that provides context
function GenericTwoStepModal({
  firstStepConfig,
  secondStepConfig,
  onFirstStepSubmit,
  onSecondStepSubmit,
  onError,
  onSelectionError,
  ...props
}: GenericTwoStepModalProps) {
  return (
    <TwoStepModalProvider
      firstStepConfig={firstStepConfig}
      secondStepConfig={secondStepConfig}
      onFirstStepSubmit={onFirstStepSubmit}
      onSecondStepSubmit={onSecondStepSubmit}
      onError={onError}
      onSelectionError={onSelectionError}
    >
      <TwoStepModalContent {...props} />
    </TwoStepModalProvider>
  )
}

export default GenericTwoStepModal
export { FirstStep, SecondStep }
export type { GenericTwoStepModalProps, FirstStepConfig, SecondStepConfig } from './types'
