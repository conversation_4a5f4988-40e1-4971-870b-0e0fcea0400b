import { ModalHooksReturn } from 'src/hooks/useAntModal'

import GenericTwoStepModal, { SecondStepConfig } from '.'

// Example usage: Template Creation Modal
interface CreateTemplateModalProps extends Omit<ModalHooksReturn<unknown>, 'triggerProps'> {
  onTemplateCreate?: (templateName: string, baseTemplateId: string | number) => Promise<void>
  templateTableComponent?: React.ReactElement
}

function CreateTemplateModalExample({
  onTemplateCreate,
  templateTableComponent,
  ...modalProps
}: CreateTemplateModalProps) {
  const firstStepConfig = {
    label: 'Template Name',
    placeholder: 'Enter template name',
    maxLength: 20,
    inputId: 'templateName',
    formItemName: 'templateName',
  }

  const secondStepConfig: SecondStepConfig = {
    title: 'Select Base Template',
    description: 'Or create a new template from scratch',
    createNewOptionText: 'Create new template',
    tableProps: {
      dataSource: [],
      columns: [],
    },
  }

  const handleFirstStepSubmit = async (templateName: string) => {
    // Custom validation logic for template name
    if (templateName.length < 3) {
      throw new Error('Template name must be at least 3 characters')
    }
  }

  const handleSecondStepSubmit = async (selectedId: string | number, templateName: string) => {
    if (onTemplateCreate) {
      await onTemplateCreate(templateName, selectedId)
    }
  }

  const handleError = (error: unknown) => {
    const errorWithMessage = error as { message?: string }
    return errorWithMessage.message || 'Template name validation failed'
  }

  return (
    <GenericTwoStepModal
      {...modalProps}
      firstStepConfig={firstStepConfig}
      secondStepConfig={secondStepConfig}
      onFirstStepSubmit={handleFirstStepSubmit}
      onSecondStepSubmit={handleSecondStepSubmit}
      onError={handleError}
    />
  )
}

export { CreateTemplateModalExample }
export default GenericTwoStepModal
