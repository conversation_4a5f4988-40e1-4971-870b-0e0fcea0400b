import { useNavigate } from 'react-router'

import { useAppSelector } from '@/store/hook'
import { ModalHooksReturn } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { useCheckProtocolNameMutation } from 'src/services/api'

import GenericTwoStepModal, { FirstStepConfig, SecondStepConfig } from '.'

// Example usage: Protocol Creation Modal
interface CreateProtocolModalProps extends Omit<ModalHooksReturn<unknown>, 'triggerProps'> {
  // Any additional props specific to protocol creation
}

function CreateProtocolModalExample({ ...modalProps }: CreateProtocolModalProps) {
  // router
  const navigate = useNavigate()
  // redux
  const { protocols } = useAppSelector((state) => state.protocolReducer)
  const [checkProtocolNameMutation] = useCheckProtocolNameMutation()

  // Configuration for first step (protocol name input)
  const firstStepConfig: FirstStepConfig = {
    label: i18n.t('titles.create_protocol_name'),
    placeholder: i18n.t('form_placeholders.enter_variable', {
      variable: i18n.t('titles.protocol_name'),
      joinArrays: ' ',
    }),
    maxLength: 16,
  }

  // Configuration for second step (protocol selection)
  const secondStepConfig: SecondStepConfig<ProtocolType> = {
    title: 'Select Protocol Template',
    description: i18n.t('paragraphs.create_new_protocol.third'),
    createNewOptionText: i18n.t('paragraphs.create_new_protocol.first'),
    tableProps: {
      dataSource: protocols,
      columns: [], // Replace with actual columns
    },
  }

  // API and business logic handlers
  const handleFirstStepSubmit = async (protocolName: string) => {
    await checkProtocolNameMutation({ name: protocolName }).unwrap()
  }

  const handleSecondStepSubmit = async (selectedId: string | number, protocolName: string) => {
    navigate('create', { state: { protocolName, copyProtocolID: selectedId } })
  }

  // Error handling
  const handleError = (error: unknown) => {
    if (typeof error === 'object' && error !== null) {
      if ('status' in error) {
        const statusError = error as { status: number }
        return statusError.status === 409
          ? i18n.t('form_rules.variable_unique', {
            variable: i18n.t('titles.protocol_name'),
            joinArrays: ' ',
          })
          : i18n.t('error_contents.server_error')
      }
    }
    return i18n.t('form_rules.enter_variable', {
      variable: i18n.t('plain_texts.protocol_name'),
      joinArrays: ' ',
    })
  }

  const handleSelectionError = () => {
    return i18n.t('error_contents.choose_protocol', {
      item: i18n.t('titles.protocols').toLocaleLowerCase(),
      joinArrays: ' ',
    })
  }

  return (
    <GenericTwoStepModal
      {...modalProps}
      firstStepConfig={firstStepConfig}
      secondStepConfig={secondStepConfig}
      onFirstStepSubmit={handleFirstStepSubmit}
      onSecondStepSubmit={handleSecondStepSubmit}
      onError={handleError}
      onSelectionError={handleSelectionError}
    />
  )
}

// Example usage: Template Creation Modal
interface CreateTemplateModalProps extends Omit<ModalHooksReturn<unknown>, 'triggerProps'> {
  onTemplateCreate?: (templateName: string, baseTemplateId: string | number) => Promise<void>
  templateTableComponent?: React.ReactElement
}

function CreateTemplateModalExample({
  onTemplateCreate,
  templateTableComponent,
  ...modalProps
}: CreateTemplateModalProps) {
  const firstStepConfig = {
    label: 'Template Name',
    placeholder: 'Enter template name',
    maxLength: 20,
    inputId: 'templateName',
    formItemName: 'templateName',
  }

  const secondStepConfig = {
    title: 'Select Base Template',
    subtitle: 'Choose a base template to copy from',
    description: 'Or create a new template from scratch',
    createNewOptionText: 'Create new template',
    createNewOptionId: 'create-template',
    tableComponent: templateTableComponent || <div>Template table component</div>,
  }

  const handleFirstStepSubmit = async (templateName: string) => {
    // Custom validation logic for template name
    if (templateName.length < 3) {
      throw new Error('Template name must be at least 3 characters')
    }
  }

  const handleSecondStepSubmit = async (selectedId: string | number, templateName: string) => {
    if (onTemplateCreate) {
      await onTemplateCreate(templateName, selectedId)
    }
  }

  const handleError = (error: unknown) => {
    const errorWithMessage = error as { message?: string }
    return errorWithMessage.message || 'Template name validation failed'
  }

  const handleSelectionError = () => {
    return 'Please select a base template or choose to create new'
  }

  return (
    <GenericTwoStepModal
      {...modalProps}
      firstStepConfig={firstStepConfig}
      secondStepConfig={secondStepConfig}
      onFirstStepSubmit={handleFirstStepSubmit}
      onSecondStepSubmit={handleSecondStepSubmit}
      onError={handleError}
      onSelectionError={handleSelectionError}
    />
  )
}

export { CreateProtocolModalExample, CreateTemplateModalExample }
export default GenericTwoStepModal
