import { Dispatch, SetStateAction } from 'react'

import { Tabs, TabsProps } from 'antd'

import InformationList from 'src/components/Form/InformationList'
import { workListConfigHeader, protocolConfigHeader } from 'src/context/moduleList'
import i18n from 'src/i18n'
import { useAppSelector } from 'src/store/hook'

interface Props {
  tabItems: TabsProps['items']
  configData: ProtocolDetailType | WorklistDetailType | null
  tabKey?: string
  setTabKey?: Dispatch<SetStateAction<string>>
  informationType: 'worklist' | 'protocol'
}

function ConfigLayout({
  tabItems, configData, tabKey, setTabKey, informationType,
}: Props) {
  const { worklistDetail, updateWorklistDetailCheck } = useAppSelector((state) => state.worklistReducer)

  return (
    <section className="config-layout">
      {informationType === 'worklist' ? (
        <InformationList<WorklistDetailType>
          configHeader={workListConfigHeader}
          configData={configData as WorklistDetailType}
          informationType={informationType}
        />
      ) : (
        <InformationList<ProtocolDetailType>
          configHeader={protocolConfigHeader}
          configData={configData as ProtocolDetailType}
          informationType={informationType}
        />
      )}
      <Tabs
        items={tabItems}
        onTabClick={(activeKey) => setTabKey?.(activeKey)}
        activeKey={tabKey}
        className={`${updateWorklistDetailCheck && !worklistDetail.use_protocol && 'setting-change-info'}`}
        data-content={i18n.t('paragraphs.config_changed')}
      />
    </section>
  )
}

export default ConfigLayout
