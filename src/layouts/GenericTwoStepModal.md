# GenericTwoStepModal

基於 `CreateNewProtocolModal.tsx` 創建的通用兩步驟 Modal 組件，保留了原始的樣式和功能結構，但將 API 和業務邏輯抽離為 props，使其可重用於不同場景。

## 特性

- 🎨 **保留原始樣式**: 完全保留了原始 Modal 的 UI 樣式和佈局
- 🔧 **可配置**: 通過 props 配置不同的文字、驗證規則和業務邏輯
- 🚀 **可重用**: 可用於任何需要兩步驟流程的 Modal
- 📱 **響應式**: 保持原始的響應式設計
- 🌐 **國際化**: 支持 i18n 文字配置

## 使用方式

### 基本用法

```tsx
import { GenericTwoStepModal } from 'src/layouts'
import { useAntModal } from 'src/hooks/useAntModal'

function MyComponent() {
  const modal = useAntModal({
    modalProps: {
      title: 'Create New Item',
      centered: true,
    },
  })

  const firstStepConfig = {
    label: 'Item Name',
    placeholder: 'Enter item name',
    maxLength: 16,
    inputId: 'itemName',
    formItemName: 'itemName',
  }

  const secondStepConfig = {
    title: 'Select Template',
    subtitle: 'Choose a template to copy from',
    description: 'Or create from scratch',
    createNewOptionText: 'Create new item',
    createNewOptionId: 'create-item',
    tableComponent: <MyTableComponent />,
  }

  const handleFirstStepSubmit = async (value: string) => {
    // 驗證邏輯
    await validateItemName(value)
  }

  const handleSecondStepSubmit = async (selectedId: string | number, inputValue: string) => {
    // 提交邏輯
    await createItem(inputValue, selectedId)
  }

  return (
    <GenericTwoStepModal
      {...modal}
      firstStepConfig={firstStepConfig}
      secondStepConfig={secondStepConfig}
      onFirstStepSubmit={handleFirstStepSubmit}
      onSecondStepSubmit={handleSecondStepSubmit}
    />
  )
}
```

## Props 介面

### GenericTwoStepModalProps

| 屬性 | 類型 | 必需 | 描述 |
|------|------|------|------|
| `firstStepConfig` | `FirstStepConfig` | ✅ | 第一步驟的配置 |
| `secondStepConfig` | `SecondStepConfig` | ✅ | 第二步驟的配置 |
| `onFirstStepSubmit` | `(value: string) => Promise<void>` | ✅ | 第一步驟提交處理函數 |
| `onSecondStepSubmit` | `(selectedId: string \| number, inputValue: string) => Promise<void>` | ✅ | 第二步驟提交處理函數 |
| `modalTitle` | `string` | ❌ | Modal 標題 |
| `onError` | `(error: any) => string` | ❌ | 錯誤處理函數 |
| `onSelectionError` | `() => string` | ❌ | 選擇錯誤處理函數 |

### FirstStepConfig

| 屬性 | 類型 | 必需 | 描述 |
|------|------|------|------|
| `label` | `string` | ✅ | 輸入框標籤 |
| `placeholder` | `string` | ✅ | 輸入框佔位符 |
| `inputId` | `string` | ✅ | 輸入框 ID |
| `formItemName` | `string` | ✅ | 表單項名稱 |
| `maxLength` | `number` | ❌ | 最大長度 (預設: 16) |
| `validationRules` | `any[]` | ❌ | 驗證規則 |

### SecondStepConfig

| 屬性 | 類型 | 必需 | 描述 |
|------|------|------|------|
| `title` | `string` | ✅ | 第二步驟標題 |
| `subtitle` | `string` | ✅ | 副標題 |
| `description` | `string` | ✅ | 描述文字 |
| `createNewOptionText` | `string` | ✅ | 創建新項目選項文字 |
| `createNewOptionId` | `string` | ✅ | 創建新項目選項 ID |
| `tableComponent` | `ReactNode` | ✅ | 表格組件 |

## 範例

查看 `GenericTwoStepModal.example.tsx` 文件以獲取完整的使用範例，包括：

1. **Protocol Creation Modal**: 基於原始 `CreateNewProtocolModal` 的實現
2. **Template Creation Modal**: 展示如何用於其他場景

## 遷移指南

如果您想將現有的 `CreateNewProtocolModal` 遷移到使用這個通用組件：

1. 將 API 調用邏輯移到父組件
2. 配置 `firstStepConfig` 和 `secondStepConfig`
3. 實現 `onFirstStepSubmit` 和 `onSecondStepSubmit` 函數
4. 替換原始組件的使用

## 注意事項

- 保持了原始的 CSS 類名和樣式結構
- 支持所有原始的 Modal 功能（離開確認、錯誤處理等）
- 表格組件需要自行處理選擇邏輯並通過 props 傳遞
- 建議在表格組件中使用 `radioRefs` 來管理 radio 按鈕狀態
