import {
  useState, useRef, useEffect, ReactNode,
} from 'react'

import {
  Button, ConfigProvider, Flex, Form, Input, Modal,
} from 'antd'

import { ModalHooksReturn, useAntModal } from 'src/hooks/useAntModal'
import i18n from 'src/i18n'
import { color } from 'src/utils/variables'
import { requiredRules } from 'src/utils/verify'

import 'src/styles/components/modal.css'

interface FirstStepConfig {
  label: string
  placeholder: string
  maxLength?: number
  inputId: string
  formItemName: string
  validationRules?: any[]
}

interface SecondStepConfig {
  title: string
  subtitle: string
  description: string
  createNewOptionText: string
  createNewOptionId: string
  tableComponent: ReactNode
}

interface GenericTwoStepModalProps extends Omit<ModalHooksReturn<unknown>, 'triggerProps'> {
  // Configuration for first step
  firstStepConfig: FirstStepConfig

  // Configuration for second step
  secondStepConfig: SecondStepConfig

  // API and business logic functions
  onFirstStepSubmit: (value: string) => Promise<void>
  onSecondStepSubmit: (selectedId: string | number, inputValue: string) => Promise<void>

  // Modal titles and texts
  leaveConfirmationTitle?: string
  leaveConfirmationContent?: string
  errorModalTitle?: string

  // Button texts
  cancelButtonText?: string
  nextButtonText?: string
  leaveButtonText?: string
  okButtonText?: string

  // Error handling
  onError?: (error: any) => string
  onSelectionError?: () => string
}

function GenericTwoStepModal({
  firstStepConfig,
  secondStepConfig,
  onFirstStepSubmit,
  onSecondStepSubmit,
  leaveConfirmationTitle = i18n.t('modal_titles.leave_page_confirmation'),
  leaveConfirmationContent = i18n.t('modal_contents.leave_page_confirmation'),
  errorModalTitle,
  cancelButtonText = i18n.t('buttons.cancel'),
  nextButtonText = i18n.t('buttons.next'),
  leaveButtonText = i18n.t('buttons.leave'),
  okButtonText = i18n.t('buttons.ok'),
  onError,
  onSelectionError,
  ...props
}: GenericTwoStepModalProps) {
  // ref
  const radioRefs: { current: { [key: string]: HTMLInputElement | null } } = useRef({})

  // form
  const [form] = Form.useForm()

  // state
  const [selectedId, setSelectedId] = useState<number | string>('')
  const [inputValue, setInputValue] = useState<string>()
  const [currentPage, setCurrentPage] = useState<1 | 2>(1)
  const [errorText, setErrorText] = useState<string>('')
  const [isVerify, setIsVerify] = useState({ check: false, text: '' })

  // modal
  const leaveModal = useAntModal({
    modalProps: {
      title: leaveConfirmationTitle,
      okText: leaveButtonText,
      onOk: async () => {
        props.dismiss()
        setCurrentPage(1)
        form.resetFields()
      },
      className: 'confirm-modal',
      centered: true,
    },
  })

  const requiredModal = useAntModal({
    modalProps: {
      centered: true,
      className: 'confirm-modal',
      okText: okButtonText,
      footer: null,
      styles: {
        body: { padding: '7.5rem 2.5rem' },
      },
    },
  })

  const handleRadioChange = (value: number | string) => {
    setSelectedId(value)
  }

  const handleCancel = () => {
    if (currentPage === 1) {
      props.dismiss()
      setCurrentPage(1)
    } else {
      leaveModal.trigger()
      setSelectedId('')
    }
    setIsVerify({ check: false, text: '' })
    form.resetFields()
  }

  const handleSecondPage = async () => {
    await onSecondStepSubmit(selectedId, inputValue!)
    setCurrentPage(1)
  }

  const handleFirstPageError = (e: any) => {
    if (typeof e === 'object' && e !== null) {
      const text: string = onError ? onError(e) : 'An error occurred'
      setIsVerify({ check: true, text })
    }
  }

  const handleFirstPage = async () => {
    try {
      const formValue = form.getFieldsValue()[firstStepConfig.formItemName]
      await onFirstStepSubmit(formValue)
      setInputValue(formValue)
      setCurrentPage(2)
    } catch (e) {
      handleFirstPageError(e)
    }
  }

  const handleError = () => {
    const errorMessage = onSelectionError ? onSelectionError() : 'Please make a selection'
    setErrorText(errorMessage)
    requiredModal.trigger()
  }

  const handleFinish = async () => {
    try {
      await form.validateFields()
      if (currentPage === 1) {
        await handleFirstPage()
      } else {
        await handleSecondPage()
      }
    } catch (e) {
      handleError()
    }
  }

  useEffect(() => {
    if (!props.modalProps.open) {
      return () => {
        form.resetFields()
      }
    }
    return () => { }
  }, [props.modalProps.open])

  return (
    <>
      <ConfigProvider theme={{
        components: {
          Table: {
            borderColor: 'rgba(192, 192, 192, 0.30)',
            headerBg: color.gray[3],
            colorBgContainer: color.gray[3],
            rowHoverBg: color.gray[1].default,
            rowSelectedHoverBg: color.gray[1].default,
          },
        },
      }}
      >
        <Modal
          {...props.modalProps}
          onCancel={handleCancel}
          footer={[
            <Button
              htmlType="button"
              variant="outlined"
              color="primary"
              onClick={handleCancel}
              key="cancel"
            >
              {cancelButtonText}
            </Button>,
            <Button
              htmlType="submit"
              variant="outlined"
              color="primary"
              onClick={handleFinish}
              key="next"
            >
              {nextButtonText}
            </Button>,
          ]}
        >
          <Form
            form={form}
            colon={false}
            name="generic-two-step-form"
            layout="vertical"
            requiredMark={false}
            onFinish={handleFinish}
          >
            {
              currentPage === 1
              && (
                <Form.Item
                  label={<span>{firstStepConfig.label}</span>}
                  name={firstStepConfig.formItemName}
                  htmlFor={firstStepConfig.inputId}
                  layout="vertical"
                  validateStatus={isVerify.check ? 'error' : undefined}
                  help={isVerify.text}
                  rules={firstStepConfig.validationRules || [requiredRules()]}
                  style={{ width: '35%', minWidth: 300, textAlign: 'center' }}
                >
                  <Input
                    id={firstStepConfig.inputId}
                    placeholder={firstStepConfig.placeholder}
                    style={{ textAlign: 'center' }}
                    maxLength={firstStepConfig.maxLength || 16}
                  />
                </Form.Item>
              )
            }
            {
              currentPage === 2
              && (
                <Form.Item
                  name="radio-group"
                  className="radio-group"
                  style={{ marginBottom: 0, width: '100%' }}
                  rules={[requiredRules()]}
                >
                  <main id="radio-group" style={{ height: '100%' }}>
                    <Flex vertical justify="center" align="center">
                      <div>
                        <label htmlFor={secondStepConfig.createNewOptionId} className="radio-button">
                          <span>
                            <input
                              id={secondStepConfig.createNewOptionId}
                              type="radio"
                              name="radio-group"
                              onChange={(e) => handleRadioChange(e.target.id)}
                              ref={(input) => { radioRefs.current[secondStepConfig.createNewOptionId] = input }}
                            />
                            <span />
                          </span>
                          <span className="radio-button-text">
                            {secondStepConfig.createNewOptionText}
                          </span>
                        </label>
                      </div>
                      <p>{secondStepConfig.subtitle}</p>
                      <h3>{secondStepConfig.description}</h3>

                    </Flex>
                    {secondStepConfig.tableComponent}
                  </main>
                </Form.Item>
              )
            }
          </Form>
        </Modal>
      </ConfigProvider>

      {/* leave modal */}
      <Modal {...leaveModal.modalProps}>
        {leaveConfirmationContent}
      </Modal>

      {/* required modal */}
      <Modal {...requiredModal.modalProps}>
        {errorText}
      </Modal>
    </>
  )
}

export default GenericTwoStepModal
