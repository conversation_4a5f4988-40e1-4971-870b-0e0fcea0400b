/* ------------------------ Queue Types ------------------------ */

// Response
export interface GetRsStructureRes {
  structure: {
    id: number,
    name: string,
    volumn_type: string,
    color: string,
    contour_data: boolean
  }[]
} // ✅

/* ------------------------ Template Types ------------------------ */

export interface UpdateRsTemplateReq extends RsTemplateDetailType {
  template_id: number
}
